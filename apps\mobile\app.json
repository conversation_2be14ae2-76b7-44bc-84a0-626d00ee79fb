{"expo": {"name": "Tap2Go", "slug": "tap2go-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#f3a823"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tap2go.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#f3a823"}, "package": "com.tap2go.mobile", "edgeToEdgeEnabled": true, "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "tap2go-mobile"}}}}