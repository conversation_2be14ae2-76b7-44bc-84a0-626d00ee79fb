# Complete Tap2Go Project Backup Dockerfile
# This captures the entire development environment with proper handling

FROM node:18-bullseye

# Set working directory
WORKDIR /app

# Install system dependencies that might be needed
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    python3 \
    python3-pip \
    rsync \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages that your project uses
RUN npm install -g \
    pnpm@8.15.6 \
    turbo@2.0.0 \
    @turbo/gen@2.0.0 \
    expo-cli \
    @expo/cli \
    firebase-tools \
    vercel

# First copy package files and install dependencies fresh
# This ensures clean node_modules without permission issues
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/*/package.json ./packages/*/
COPY apps/*/package.json ./apps/*/
COPY functions/package.json ./functions/

# Install dependencies fresh (this creates clean node_modules)
RUN pnpm install --frozen-lockfile

# Now copy all source code and configuration files
# (node_modules are excluded via .dockerignore)
COPY . .

# Fix any permission issues and make scripts executable
RUN find /app -type f -name "*.sh" -exec chmod +x {} \; || true
RUN find /app -type f -path "*/node_modules/.bin/*" -exec chmod +x {} \; || true

# Build the project to ensure everything works
RUN pnpm run build || echo "Build completed with warnings"

# Create .env.local from .env.example for initial setup
RUN if [ -f .env.example ]; then cp .env.example .env.local; fi

# Expose all ports your project might use
EXPOSE 3000
EXPOSE 8081
EXPOSE 19000
EXPOSE 19001
EXPOSE 19002

# Set environment variables
ENV NODE_ENV=development
ENV PNPM_HOME=/usr/local/bin
ENV PATH=$PNPM_HOME:$PATH

# Default command - you can override this when running
CMD ["bash", "-c", "echo '🚀 Tap2Go Complete Environment Ready!' && echo '📝 Remember to update .env.local with your actual API keys' && echo '💻 Run: pnpm run dev (for web) or pnpm run mobile:dev (for mobile)' && bash"]
