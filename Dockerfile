# Complete Tap2Go Project Backup Dockerfile
# This captures the entire 5GB+ development environment

FROM node:18-bullseye

# Set working directory
WORKDIR /app

# Install system dependencies that might be needed
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages that your project uses
RUN npm install -g \
    pnpm@8.15.6 \
    turbo@2.0.0 \
    @turbo/gen@2.0.0 \
    expo-cli \
    @expo/cli \
    firebase-tools \
    vercel

# Copy the ENTIRE project (100% portability)
# This includes: source code, node_modules, build outputs, cache, everything!
COPY . .

# Fix any permission issues from Windows to Linux
RUN find /app -type f -name "*.sh" -exec chmod +x {} \; || true
RUN find /app -type f -path "*/node_modules/.bin/*" -exec chmod +x {} \; || true

# Create .env.local from .env.example for initial setup
RUN if [ -f .env.example ]; then cp .env.example .env.local; fi

# Expose all ports your project might use
EXPOSE 3000
EXPOSE 8081
EXPOSE 19000
EXPOSE 19001
EXPOSE 19002

# Set environment variables
ENV NODE_ENV=development
ENV PNPM_HOME=/usr/local/bin
ENV PATH=$PNPM_HOME:$PATH

# Default command - you can override this when running
CMD ["bash", "-c", "echo '🚀 Tap2Go Complete Environment Ready!' && echo '📝 Remember to update .env.local with your actual API keys' && echo '💻 Run: pnpm run dev (for web) or pnpm run mobile:dev (for mobile)' && bash"]
